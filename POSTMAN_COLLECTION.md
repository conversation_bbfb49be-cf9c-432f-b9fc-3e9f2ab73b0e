# Balance API - Postman 集合

这个文档提供了完整的 Postman API 调用示例，支持所有新增字段和功能。

## 基础配置

**Base URL**: `http://localhost:8080/api/v1`

## 1. Token 管理

### 1.1 创建 Token

**POST** `/tokens`

```json
{
  "id": "token-001",
  "apikey": "your-api-key-123",
  "apisecret": "your-api-secret-456",
  "apitoken": "your-api-token-789",
  "limit": 1000,
  "original_type": 3,
  "capability": ["stock_us", "stock_a"]
}
```

### 1.2 创建多类型组合 Token

**POST** `/tokens`

```json
{
  "id": "token-combo-001",
  "apikey": "combo-api-key",
  "apisecret": "combo-api-secret",
  "apitoken": "combo-api-token",
  "limit": 1500,
  "original_type": 3,
  "types": [3, 4],
  "capability": ["stock_us", "stock_hk", "stock_a"]
}
```

### 1.3 获取所有 Token

**GET** `/tokens`

### 1.4 获取指定 Token

**GET** `/tokens/token-001`

### 1.5 更新 Token

**PUT** `/tokens/token-001`

```json
{
  "limit": 1200
}
```

### 1.6 删除 Token

**DELETE** `/tokens/token-001`

## 2. Receiver 管理

### 2.1 创建 Receiver

**POST** `/receivers`

```json
{
  "id": "receiver-001",
  "original_type": 3,
  "capability": ["stock_us", "stock_a"]
}
```

### 2.2 创建默认 Receiver (stock_a)

**POST** `/receivers`

```json
{
  "id": "receiver-default-001"
}
```

### 2.3 获取所有 Receiver

**GET** `/receivers`

### 2.4 获取指定 Receiver

**GET** `/receivers/receiver-001`

### 2.5 更新 Receiver

**PUT** `/receivers/receiver-001`

```json
{
  "used": 100
}
```

### 2.6 删除 Receiver

**DELETE** `/receivers/receiver-001`

## 3. Product 管理

### 3.1 创建 Product

**POST** `/products`

```json
{
  "code": "AAPL",
  "symbol": "Apple Inc.",
  "type": 3
}
```

### 3.2 批量创建 Product

**POST** `/products/batch`

```json
{
  "type": 3,
  "codes": [
    {
      "code": "AAPL",
      "symbol": "Apple Inc.",
      "type": 3
    },
    {
      "code": "GOOGL",
      "symbol": "Alphabet Inc.",
      "type": 3
    },
    {
      "code": "MSFT",
      "symbol": "Microsoft Corporation",
      "type": 3
    }
  ]
}
```

### 3.3 获取所有 Product

**GET** `/products`

### 3.4 获取指定 Product

**GET** `/products/AAPL`

### 3.5 更新 Product

**PUT** `/products/AAPL`

```json
{
  "symbol": "Apple Inc. Updated",
  "type": 3
}
```

### 3.6 删除 Product

**DELETE** `/products/AAPL`

### 3.7 分配产品

**POST** `/products/allocate`

```json
{
  "code": "AAPL",
  "symbol": "Apple Inc.",
  "type": 3
}
```

### 3.8 获取产品统计

**GET** `/products/stats`

## 4. 系统配置

### 4.1 获取系统状态

**GET** `/system/status`

### 4.2 启用混用模式

**PUT** `/system/mixed-mode`

```json
{
  "enable_mixed_mode": true
}
```

### 4.3 禁用混用模式

**PUT** `/system/mixed-mode`

```json
{
  "enable_mixed_mode": false
}
```

### 4.4 获取能力信息

**GET** `/system/capabilities/3`

## 5. 完整测试流程

### 步骤 1: 创建 Token
```bash
curl -X POST http://localhost:8080/api/v1/tokens \
  -H "Content-Type: application/json" \
  -d '{
    "id": "token-test-001",
    "apikey": "test-api-key",
    "apisecret": "test-api-secret",
    "apitoken": "test-api-token",
    "limit": 1000,
    "original_type": 3,
    "capability": ["stock_us", "stock_a"]
  }'
```

### 步骤 2: 创建 Receiver
```bash
curl -X POST http://localhost:8080/api/v1/receivers \
  -H "Content-Type: application/json" \
  -d '{
    "id": "receiver-test-001",
    "original_type": 3,
    "capability": ["stock_us", "stock_a"]
  }'
```

### 步骤 3: 创建 Product
```bash
curl -X POST http://localhost:8080/api/v1/products \
  -H "Content-Type: application/json" \
  -d '{
    "code": "AAPL",
    "symbol": "Apple Inc.",
    "type": 3
  }'
```

### 步骤 4: 分配产品
```bash
curl -X POST http://localhost:8080/api/v1/products/allocate \
  -H "Content-Type: application/json" \
  -d '{
    "code": "AAPL",
    "symbol": "Apple Inc.",
    "type": 3
  }'
```

### 步骤 5: 查看统计信息
```bash
curl http://localhost:8080/api/v1/products/stats
```

## 6. 错误处理

### 常见错误响应

#### 409 - 资源已存在
```json
{
  "code": 409,
  "message": "Token ID already exists"
}
```

#### 404 - 资源不存在
```json
{
  "code": 404,
  "message": "Token not found"
}
```

#### 400 - 请求参数错误
```json
{
  "code": 400,
  "message": "Invalid request: Key: 'TokenRequest.ID' Error:Field validation for 'ID' failed on the 'required' tag"
}
```

## 7. 字段说明

### Token 字段
- `id`: 用户自定义的唯一标识符
- `apikey`: API 密钥
- `apisecret`: API 密钥
- `apitoken`: API 令牌
- `limit`: 使用限额 (默认: 500)
- `original_type`: 原始类型 (1-6)
- `types`: 多类型组合 (可选)
- `capability`: 能力列表 (可选)

### Receiver 字段
- `id`: 用户自定义的唯一标识符
- `original_type`: 原始类型 (可选，默认: 5-stock_a)
- `capability`: 能力列表 (可选)

### Product 字段
- `code`: 产品代码 (必需)
- `symbol`: 产品符号 (可选)
- `type`: 产品类型 (1-6，必需)

## 8. 产品类型映射

| 类型 | 名称 | 支持能力 |
|------|------|----------|
| 1 | forex | forex |
| 2 | energy | energy |
| 3 | stock_us | stock_us, stock_a |
| 4 | stock_hk | stock_hk, stock_a |
| 5 | stock_a | stock_a |
| 6 | crypto | crypto |
