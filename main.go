package main

func main() {
	balancer := NewBalancer()

	// 测试场景1：初始化不同类型的令牌，展示能力计算
	println("=== 初始化令牌（展示能力系统）===")
	token1 := balancer.AddNewToken(1000, 1) // forex类型
	token2 := balancer.AddNewToken(500, 2)  // energy类型
	token3 := balancer.AddNewToken(800, 3)  // stock_us类型（会自动包含stock_a能力）
	token4 := balancer.AddNewToken(600, 4)  // stock_hk类型（会自动包含stock_a能力）

	_, desc1 := balancer.GetCapabilityInfo(1)
	_, desc2 := balancer.GetCapabilityInfo(2)
	_, desc3 := balancer.GetCapabilityInfo(3)
	_, desc4 := balancer.GetCapabilityInfo(4)

	println("创建Token:", token1.ID, "原始类型:", token1.OriginalType, "支持能力:", desc1)
	println("创建Token:", token2.ID, "原始类型:", token2.OriginalType, "支持能力:", desc2)
	println("创建Token:", token3.ID, "原始类型:", token3.OriginalType, "支持能力:", desc3)
	println("创建Token:", token4.ID, "原始类型:", token4.OriginalType, "支持能力:", desc4)

	// 测试场景2：初始化接收器
	println("\n=== 初始化接收器 ===")
	receiver1 := balancer.AddNewReceiver(1) // forex类型
	receiver2 := balancer.AddNewReceiver(3) // stock_us类型
	println("创建Receiver:", receiver1.ID, "原始类型:", receiver1.OriginalType, "绑定Token:", receiver1.TokenID)
	println("创建Receiver:", receiver2.ID, "原始类型:", receiver2.OriginalType, "绑定Token:", receiver2.TokenID)

	// 测试场景3：严格模式下的产品分配
	println("\n=== 严格模式产品分配测试 ===")
	println("混用模式:", balancer.EnableMixedMode)

	receiverID, tokenID := balancer.AllocateProduct(1) // 分配forex类型产品
	println("分配forex产品 -> 接收器:", receiverID, "令牌:", tokenID)

	receiverID, tokenID = balancer.AllocateProduct(3) // 分配stock_us类型产品
	println("分配stock_us产品 -> 接收器:", receiverID, "令牌:", tokenID)

	receiverID, tokenID = balancer.AllocateProduct(5) // 分配stock_a类型产品（应该能使用stock_us的token）
	println("分配stock_a产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 测试场景4：启用混用模式
	println("\n=== 启用混用模式测试 ===")
	balancer.SetMixedMode(true)
	println("混用模式:", balancer.EnableMixedMode)

	receiverID, tokenID = balancer.AllocateProduct(2) // 分配energy类型产品
	println("分配energy产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 测试场景5：混用模式下的兼容性测试
	println("\n=== 混用模式兼容性测试 ===")
	// 尝试用stock_us的token处理stock_a产品（应该成功，因为stock_us token包含stock_a能力）
	receiverID, tokenID = balancer.AllocateProduct(5) // stock_a产品
	println("分配stock_a产品（使用兼容token）-> 接收器:", receiverID, "令牌:", tokenID)

	// 测试场景6：连续分配测试
	println("\n=== 连续分配测试 ===")
	for i := 0; i < 3; i++ {
		receiverID, tokenID = balancer.AllocateProduct(1)
		println("第", i+1, "次分配forex产品 -> 接收器:", receiverID, "令牌:", tokenID)
	}

	// 测试场景7：资源不足时的自动扩容
	println("\n=== 自动扩容测试 ===")
	receiverID, tokenID = balancer.AllocateProduct(6) // crypto产品（没有对应的token）
	println("分配crypto产品（自动创建）-> 接收器:", receiverID, "令牌:", tokenID)
}
