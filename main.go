package main

func main() {
	balancer := NewBalancer()

	// 测试场景1：初始化不同类型的令牌，展示新的能力规则
	println("=== 初始化令牌（新能力规则）===")
	token1 := balancer.AddNewToken(1000, 1) // forex类型
	token2 := balancer.AddNewToken(500, 2)  // energy类型
	token3 := balancer.AddNewToken(800, 3)  // stock_us类型（支持stock_us和stock_a）
	token4 := balancer.AddNewToken(600, 4)  // stock_hk类型（支持stock_hk和stock_a）
	token5 := balancer.AddNewToken(400, 5)  // stock_a类型（只支持stock_a）

	_, desc1 := balancer.GetCapabilityInfo(1)
	_, desc2 := balancer.GetCapabilityInfo(2)
	_, desc3 := balancer.GetCapabilityInfo(3)
	_, desc4 := balancer.GetCapabilityInfo(4)
	_, desc5 := balancer.GetCapabilityInfo(5)

	println("创建Token:", token1.ID, "原始类型:", token1.OriginalType, "支持能力:", desc1)
	println("创建Token:", token2.ID, "原始类型:", token2.OriginalType, "支持能力:", desc2)
	println("创建Token:", token3.ID, "原始类型:", token3.OriginalType, "支持能力:", desc3)
	println("创建Token:", token4.ID, "原始类型:", token4.OriginalType, "支持能力:", desc4)
	println("创建Token:", token5.ID, "原始类型:", token5.OriginalType, "支持能力:", desc5)

	// 测试场景2：创建多类型组合的token (stock_us+stock_hk)
	println("\n=== 多类型组合Token ===")
	tokenCombo := balancer.AddNewTokenWithMultipleTypes(1000, []int{3, 4}) // stock_us+stock_hk
	_, descCombo := balancer.GetCombinedCapabilityInfo([]int{3, 4})
	println("创建组合Token:", tokenCombo.ID, "组合类型: stock_us+stock_hk", "支持能力:", descCombo)

	// 测试场景3：初始化接收器（包括默认参数测试）
	println("\n=== 初始化接收器 ===")
	receiver1 := balancer.AddNewReceiver(3) // stock_us类型
	receiver2 := balancer.AddNewReceiver(4) // stock_hk类型
	receiver3 := balancer.AddNewReceiver()  // 默认stock_a类型
	println("创建Receiver:", receiver1.ID, "原始类型:", receiver1.OriginalType, "绑定Token:", receiver1.TokenID)
	println("创建Receiver:", receiver2.ID, "原始类型:", receiver2.OriginalType, "绑定Token:", receiver2.TokenID)
	println("创建Receiver:", receiver3.ID, "原始类型:", receiver3.OriginalType, "(默认stock_a) 绑定Token:", receiver3.TokenID)

	// 测试场景4：产品分配测试
	println("\n=== 产品分配测试 ===")

	// 测试stock_us产品分配
	receiverID, tokenID := balancer.AllocateProduct(3) // stock_us产品
	println("分配stock_us产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 测试stock_hk产品分配
	receiverID, tokenID = balancer.AllocateProduct(4) // stock_hk产品
	println("分配stock_hk产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 测试stock_a产品分配（应该能使用多种token）
	receiverID, tokenID = balancer.AllocateProduct(5) // stock_a产品
	println("分配stock_a产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 测试使用组合token
	receiverID, tokenID = balancer.AllocateProduct(3) // stock_us产品，可能使用组合token
	println("分配stock_us产品（可能使用组合token）-> 接收器:", receiverID, "令牌:", tokenID)

	// 测试场景5：启用混用模式
	println("\n=== 启用混用模式测试 ===")
	balancer.SetMixedMode(true)
	println("混用模式:", balancer.EnableMixedMode)

	// 在混用模式下，stock_a产品可以使用任何包含stock_a能力的token
	receiverID, tokenID = balancer.AllocateProduct(5) // stock_a产品
	println("分配stock_a产品（混用模式）-> 接收器:", receiverID, "令牌:", tokenID)

	// 测试场景6：验证能力规则
	println("\n=== 验证能力规则 ===")

	// 验证stock_us token能处理stock_a产品
	println("验证: stock_us token 能处理 stock_a 产品")
	receiverID, tokenID = balancer.AllocateProduct(5) // stock_a产品，应该能使用stock_us token
	println("分配stock_a产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 验证stock_hk token能处理stock_a产品
	println("验证: stock_hk token 能处理 stock_a 产品")
	receiverID, tokenID = balancer.AllocateProduct(5) // stock_a产品，应该能使用stock_hk token
	println("分配stock_a产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 验证组合token能处理所有相关产品
	println("验证: 组合token 能处理 stock_us, stock_hk, stock_a 产品")
	receiverID, tokenID = balancer.AllocateProduct(3) // stock_us产品
	println("分配stock_us产品 -> 接收器:", receiverID, "令牌:", tokenID)

	receiverID, tokenID = balancer.AllocateProduct(4) // stock_hk产品
	println("分配stock_hk产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 测试场景7：默认receiver测试
	println("\n=== 默认Receiver测试 ===")
	defaultReceiver := balancer.AddNewReceiver() // 不传参数，应该默认为stock_a
	println("创建默认Receiver:", defaultReceiver.ID, "类型:", defaultReceiver.OriginalType, "绑定Token:", defaultReceiver.TokenID)

	// 测试场景8：资源不足时的自动扩容
	println("\n=== 自动扩容测试 ===")
	receiverID, tokenID = balancer.AllocateProduct(6) // crypto产品（没有对应的token）
	println("分配crypto产品（自动创建）-> 接收器:", receiverID, "令牌:", tokenID)
}
