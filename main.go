package main

func main() {
	balancer := NewBalancer()

	// 测试场景1：初始化不同类型的令牌
	println("=== 初始化令牌 ===")
	token1 := balancer.AddNewToken(1000, 1) // forex类型
	token2 := balancer.AddNewToken(500, 2)  // energy类型
	println("创建Token:", token1.ID, "类型:", token1.Capbility, "限额:", token1.Limit)
	println("创建Token:", token2.ID, "类型:", token2.Capbility, "限额:", token2.Limit)

	// 测试场景2：初始化接收器
	println("\n=== 初始化接收器 ===")
	receiver1 := balancer.AddNewReceiver(1) // forex类型
	receiver2 := balancer.AddNewReceiver(2) // energy类型
	println("创建Receiver:", receiver1.ID, "类型:", receiver1.Capbility, "绑定Token:", receiver1.TokenID)
	println("创建Receiver:", receiver2.ID, "类型:", receiver2.Capbility, "绑定Token:", receiver2.TokenID)

	// 测试场景3：分配不同类型的产品
	println("\n=== 产品分配测试 ===")
	receiverID, tokenID := balancer.AllocateProduct(1) // 分配forex类型产品
	println("分配forex产品 -> 接收器:", receiverID, "令牌:", tokenID)

	receiverID, tokenID = balancer.AllocateProduct(2) // 分配energy类型产品
	println("分配energy产品 -> 接收器:", receiverID, "令牌:", tokenID)

	receiverID, tokenID = balancer.AllocateProduct(3) // 分配stock_us类型产品（没有对应的token和receiver）
	println("分配stock_us产品 -> 接收器:", receiverID, "令牌:", tokenID)

	// 测试场景4：连续分配同类型产品
	println("\n=== 连续分配测试 ===")
	for i := 0; i < 3; i++ {
		receiverID, tokenID = balancer.AllocateProduct(1)
		println("第", i+1, "次分配forex产品 -> 接收器:", receiverID, "令牌:", tokenID)
	}
}
