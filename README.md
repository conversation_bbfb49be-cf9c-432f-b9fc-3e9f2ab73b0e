# Balance API - 负载均衡器管理系统

一个基于 Go 和 Gin 框架的高性能负载均衡器管理系统，支持多种产品类型的智能分配和资源管理。

## 🚀 功能特性

### 核心功能
- **智能负载均衡**：基于位运算的高效能力匹配系统
- **多类型支持**：支持 forex、energy、stock_us、stock_hk、stock_a、crypto 等产品类型
- **混用模式**：支持严格匹配和兼容匹配两种模式
- **自动扩容**：资源不足时自动创建新的 Token 和 Receiver
- **实时统计**：提供详细的使用统计和系统状态监控

### 能力规则
- **stock_us(3)**：支持 stock_us + stock_a
- **stock_hk(4)**：支持 stock_hk + stock_a  
- **stock_us+stock_hk**：支持 stock_hk + stock_us + stock_a
- **stock_a(5)**：只支持 stock_a
- **其他类型**：forex、energy、crypto 各自独立

### API 特性
- **RESTful API**：完整的 CRUD 操作支持
- **Web 管理界面**：直观的图形化管理界面
- **CORS 支持**：跨域请求支持
- **统一响应格式**：标准化的 JSON 响应

## 📁 项目结构

```
.
├── balance.go          # 核心负载均衡逻辑
├── api.go             # REST API 接口实现
├── main_api.go        # API 服务器启动入口
├── main.go            # 命令行测试入口
├── index.html         # Web 管理界面
├── go.mod             # Go 模块依赖
├── API_EXAMPLES.md    # API 使用示例
└── README.md          # 项目说明文档
```

## 🛠️ 快速开始

### 环境要求
- Go 1.21+
- Git

### 安装依赖
```bash
go mod tidy
```

### 启动 API 服务器
```bash
go run main_api.go balance.go api.go
```

服务器将在 `http://localhost:8080` 启动

### 访问 Web 界面
打开浏览器访问：`http://localhost:8080`

### 运行命令行测试
```bash
go run main.go balance.go
```

## 📚 API 文档

### 基础 URL
```
http://localhost:8080/api/v1
```

### Token 管理
- `POST /tokens` - 创建 Token (支持自定义 ID、API 密钥等)
- `GET /tokens` - 获取所有 Token
- `GET /tokens/:id` - 获取指定 Token
- `PUT /tokens/:id` - 更新 Token
- `DELETE /tokens/:id` - 删除 Token

### Receiver 管理
- `POST /receivers` - 创建 Receiver (支持自定义 ID)
- `GET /receivers` - 获取所有 Receiver
- `GET /receivers/:id` - 获取指定 Receiver
- `PUT /receivers/:id` - 更新 Receiver
- `DELETE /receivers/:id` - 删除 Receiver

### Product 管理
- `POST /products` - 创建 Product
- `GET /products` - 获取所有 Product
- `GET /products/:code` - 获取指定 Product
- `PUT /products/:code` - 更新 Product
- `DELETE /products/:code` - 删除 Product
- `POST /products/batch` - 批量创建 Product
- `POST /products/allocate` - 分配产品
- `GET /products/stats` - 获取统计信息

### 系统配置
- `GET /system/status` - 获取系统状态
- `PUT /system/mixed-mode` - 设置混用模式
- `GET /system/capabilities/:type` - 获取能力信息

## 🔧 使用示例

### 创建 Token (带新字段)
```bash
curl -X POST http://localhost:8080/api/v1/tokens \
  -H "Content-Type: application/json" \
  -d '{
    "id": "token-001",
    "apikey": "your-api-key",
    "apisecret": "your-api-secret",
    "apitoken": "your-api-token",
    "limit": 1000,
    "original_type": 3,
    "capability": ["stock_us", "stock_a"]
  }'
```

### 创建 Receiver (带新字段)
```bash
curl -X POST http://localhost:8080/api/v1/receivers \
  -H "Content-Type: application/json" \
  -d '{
    "id": "receiver-001",
    "original_type": 3,
    "capability": ["stock_us", "stock_a"]
  }'
```

### 创建 Product
```bash
curl -X POST http://localhost:8080/api/v1/products \
  -H "Content-Type: application/json" \
  -d '{
    "code": "AAPL",
    "symbol": "Apple Inc.",
    "type": 3
  }'
```

### 分配产品
```bash
curl -X POST http://localhost:8080/api/v1/products/allocate \
  -H "Content-Type: application/json" \
  -d '{
    "code": "AAPL",
    "symbol": "Apple Inc.",
    "type": 3
  }'
```

## 🎯 产品类型说明

| 类型 | 名称 | 支持能力 |
|------|------|----------|
| 1 | forex | forex |
| 2 | energy | energy |
| 3 | stock_us | stock_us, stock_a |
| 4 | stock_hk | stock_hk, stock_a |
| 5 | stock_a | stock_a |
| 6 | crypto | crypto |

## 🏗️ 架构设计

### 核心组件
1. **Balancer**：负载均衡器核心
2. **Token**：资源令牌，定义容量和能力
3. **Receiver**：接收器，处理具体请求
4. **API Layer**：RESTful API 接口层

### 关键算法
- **位运算能力匹配**：O(1) 时间复杂度的能力检查
- **按类型分组**：O(1) 产品分配效率
- **智能绑定**：自动匹配最优资源组合

### 性能特点
- **高并发**：支持大量并发请求
- **低延迟**：优化的数据结构和算法
- **内存高效**：最小化内存占用
- **可扩展**：支持动态扩容

## 🔍 监控和统计

系统提供丰富的监控指标：
- Token 使用率统计
- Receiver 负载分布
- 按类型分组的可用资源
- 系统整体健康状态

## 🚦 开发和部署

### 开发模式
```bash
export GIN_MODE=debug
go run main_api.go balance.go api.go
```

### 生产模式
```bash
export GIN_MODE=release
go build -o balance-api main_api.go balance.go api.go
./balance-api
```

## 📝 更多文档

- [API 使用示例](API_EXAMPLES.md) - 详细的 API 调用示例
- [Web 界面使用指南](index.html) - 图形化管理界面

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
