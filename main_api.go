package main

import (
	"log"
)

func main() {
	// 创建负载均衡器实例
	balancer := NewBalancer()
	
	// 初始化一些测试数据
	log.Println("初始化测试数据...")
	
	// 创建不同类型的 Token
	token1 := balancer.AddNewToken(1000, 1) // forex
	token2 := balancer.AddNewToken(800, 3)  // stock_us (支持 stock_us + stock_a)
	token3 := balancer.AddNewToken(600, 4)  // stock_hk (支持 stock_hk + stock_a)
	token4 := balancer.AddNewToken(400, 5)  // stock_a (只支持 stock_a)
	
	// 创建组合类型的 Token
	tokenCombo := balancer.AddNewTokenWithMultipleTypes(1200, []int{3, 4}) // stock_us + stock_hk
	
	log.Printf("创建了 %d 个 Token", len(balancer.Tokens))
	
	// 创建不同类型的 Receiver
	receiver1 := balancer.AddNewReceiver(1) // forex
	receiver2 := balancer.AddNewReceiver(3) // stock_us
	receiver3 := balancer.AddNewReceiver(4) // stock_hk
	receiver4 := balancer.AddNewReceiver()  // 默认 stock_a
	
	log.Printf("创建了 %d 个 Receiver", len(balancer.Receivers))
	
	// 打印初始化信息
	log.Printf("Token 信息:")
	log.Printf("  %s: 类型=%d, 限额=%d, 能力=%d", token1.ID, token1.OriginalType, token1.Limit, token1.CapabilityBits)
	log.Printf("  %s: 类型=%d, 限额=%d, 能力=%d", token2.ID, token2.OriginalType, token2.Limit, token2.CapabilityBits)
	log.Printf("  %s: 类型=%d, 限额=%d, 能力=%d", token3.ID, token3.OriginalType, token3.Limit, token3.CapabilityBits)
	log.Printf("  %s: 类型=%d, 限额=%d, 能力=%d", token4.ID, token4.OriginalType, token4.Limit, token4.CapabilityBits)
	log.Printf("  %s: 组合类型, 限额=%d, 能力=%d", tokenCombo.ID, tokenCombo.Limit, tokenCombo.CapabilityBits)
	
	log.Printf("Receiver 信息:")
	log.Printf("  %s: 类型=%d, 绑定Token=%s", receiver1.ID, receiver1.OriginalType, receiver1.TokenID)
	log.Printf("  %s: 类型=%d, 绑定Token=%s", receiver2.ID, receiver2.OriginalType, receiver2.TokenID)
	log.Printf("  %s: 类型=%d, 绑定Token=%s", receiver3.ID, receiver3.OriginalType, receiver3.TokenID)
	log.Printf("  %s: 类型=%d, 绑定Token=%s", receiver4.ID, receiver4.OriginalType, receiver4.TokenID)
	
	// 启动 API 服务器
	log.Println("启动 API 服务器在端口 8080...")
	log.Println("API 文档:")
	log.Println("  Token 管理:")
	log.Println("    POST   /api/v1/tokens          - 创建 Token")
	log.Println("    GET    /api/v1/tokens          - 获取所有 Token")
	log.Println("    GET    /api/v1/tokens/:id      - 获取指定 Token")
	log.Println("    PUT    /api/v1/tokens/:id      - 更新 Token")
	log.Println("    DELETE /api/v1/tokens/:id      - 删除 Token")
	log.Println("  Receiver 管理:")
	log.Println("    POST   /api/v1/receivers       - 创建 Receiver")
	log.Println("    GET    /api/v1/receivers       - 获取所有 Receiver")
	log.Println("    GET    /api/v1/receivers/:id   - 获取指定 Receiver")
	log.Println("    PUT    /api/v1/receivers/:id   - 更新 Receiver")
	log.Println("    DELETE /api/v1/receivers/:id   - 删除 Receiver")
	log.Println("  Product 管理:")
	log.Println("    POST   /api/v1/products/allocate - 分配产品")
	log.Println("    GET    /api/v1/products/stats    - 获取统计信息")
	log.Println("  系统配置:")
	log.Println("    GET    /api/v1/system/status           - 获取系统状态")
	log.Println("    PUT    /api/v1/system/mixed-mode       - 设置混用模式")
	log.Println("    GET    /api/v1/system/capabilities/:type - 获取能力信息")
	
	StartAPIServer(balancer, "8080")
}
