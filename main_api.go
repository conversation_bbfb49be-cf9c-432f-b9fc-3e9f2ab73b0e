package main

import (
	"log"
)

func main() {
	// 创建负载均衡器实例
	balancer := NewBalancer()

	// 初始化一些测试数据
	log.Println("初始化测试数据...")

	// 创建不同类型的 Token
	token1 := balancer.AddNewTokenWithCapabilities("token-1", "", "", "", 1000, []string{"forex"})
	token2 := balancer.AddNewTokenWithCapabilities("token-2", "", "", "", 800, []string{"stock_us", "stock_a"})
	token3 := balancer.AddNewTokenWithCapabilities("token-3", "", "", "", 600, []string{"stock_hk", "stock_a"})
	token4 := balancer.AddNewTokenWithCapabilities("token-4", "", "", "", 400, []string{"stock_a"})

	// 创建组合类型的 Token
	tokenCombo := balancer.AddNewTokenWithCapabilities("token-5", "", "", "", 1200, []string{"stock_us", "stock_hk", "stock_a"})

	log.Printf("创建了 %d 个 Token", len(balancer.Tokens))

	// 创建不同类型的 Receiver
	receiver1 := balancer.AddNewReceiverWithCapabilities("receiver-1", []string{"forex"})
	receiver2 := balancer.AddNewReceiverWithCapabilities("receiver-2", []string{"stock_us", "stock_a"})
	receiver3 := balancer.AddNewReceiverWithCapabilities("receiver-3", []string{"stock_hk", "stock_a"})
	receiver4 := balancer.AddNewReceiverWithCapabilities("receiver-4", []string{"stock_a"})

	log.Printf("创建了 %d 个 Receiver", len(balancer.Receivers))

	// 打印初始化信息
	log.Printf("Token 信息:")
	log.Printf("  %s: 能力=%v, 限额=%d", token1.ID, token1.Capability, token1.Limit)
	log.Printf("  %s: 能力=%v, 限额=%d", token2.ID, token2.Capability, token2.Limit)
	log.Printf("  %s: 能力=%v, 限额=%d", token3.ID, token3.Capability, token3.Limit)
	log.Printf("  %s: 能力=%v, 限额=%d", token4.ID, token4.Capability, token4.Limit)
	log.Printf("  %s: 能力=%v, 限额=%d", tokenCombo.ID, tokenCombo.Capability, tokenCombo.Limit)

	log.Printf("Receiver 信息:")
	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver1.ID, receiver1.Capability, receiver1.TokenID)
	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver2.ID, receiver2.Capability, receiver2.TokenID)
	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver3.ID, receiver3.Capability, receiver3.TokenID)
	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver4.ID, receiver4.Capability, receiver4.TokenID)

	// 启动 API 服务器
	log.Println("启动 API 服务器在端口 8080...")
	log.Println("API 文档:")
	log.Println("  Token 管理:")
	log.Println("    POST   /api/v1/tokens          - 创建 Token")
	log.Println("    GET    /api/v1/tokens          - 获取所有 Token")
	log.Println("    GET    /api/v1/tokens/:id      - 获取指定 Token")
	log.Println("    PUT    /api/v1/tokens/:id      - 更新 Token")
	log.Println("    DELETE /api/v1/tokens/:id      - 删除 Token")
	log.Println("  Receiver 管理:")
	log.Println("    POST   /api/v1/receivers       - 创建 Receiver")
	log.Println("    GET    /api/v1/receivers       - 获取所有 Receiver")
	log.Println("    GET    /api/v1/receivers/:id   - 获取指定 Receiver")
	log.Println("    PUT    /api/v1/receivers/:id   - 更新 Receiver")
	log.Println("    DELETE /api/v1/receivers/:id   - 删除 Receiver")
	log.Println("  Product 管理:")
	log.Println("    POST   /api/v1/products/allocate - 分配产品")
	log.Println("    GET    /api/v1/products/stats    - 获取统计信息")
	log.Println("  系统配置:")
	log.Println("    GET    /api/v1/system/status           - 获取系统状态")
	log.Println("    PUT    /api/v1/system/mixed-mode       - 设置混用模式")

	StartAPIServer(balancer, "8080")
}
