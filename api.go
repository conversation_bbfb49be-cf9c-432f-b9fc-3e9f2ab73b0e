package main

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// API 响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// Token 请求结构
type TokenRequest struct {
	Limit        int   `json:"limit" binding:"required,min=1"`
	OriginalType int   `json:"original_type" binding:"required,min=1,max=6"`
	Types        []int `json:"types,omitempty"` // 用于多类型组合
}

// Receiver 请求结构
type ReceiverRequest struct {
	OriginalType *int `json:"original_type,omitempty"` // 可选，不传则默认为stock_a
}

// Product 分配请求结构
type ProductRequest struct {
	ProductType int `json:"product_type" binding:"required,min=1,max=6"`
}

// 全局负载均衡器实例
var globalBalancer *Balancer

// 初始化 API 路由
func setupRoutes() *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// CORS 中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 静态文件服务
	r.Static("/static", "./")
	r.StaticFile("/", "./index.html")

	// API 版本分组
	v1 := r.Group("/api/v1")
	{
		// Token 相关路由
		tokens := v1.Group("/tokens")
		{
			tokens.POST("", createToken)
			tokens.GET("", listTokens)
			tokens.GET("/:id", getToken)
			tokens.PUT("/:id", updateToken)
			tokens.DELETE("/:id", deleteToken)
		}

		// Receiver 相关路由
		receivers := v1.Group("/receivers")
		{
			receivers.POST("", createReceiver)
			receivers.GET("", listReceivers)
			receivers.GET("/:id", getReceiver)
			receivers.PUT("/:id", updateReceiver)
			receivers.DELETE("/:id", deleteReceiver)
		}

		// Product 相关路由
		products := v1.Group("/products")
		{
			products.POST("/allocate", allocateProduct)
			products.GET("/stats", getProductStats)
		}

		// 系统配置路由
		system := v1.Group("/system")
		{
			system.GET("/status", getSystemStatus)
			system.PUT("/mixed-mode", setMixedMode)
			system.GET("/capabilities/:type", getCapabilities)
		}
	}

	return r
}

// Token CRUD 操作

// 创建 Token
func createToken(c *gin.Context) {
	var req TokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	var token *Token
	if len(req.Types) > 0 {
		// 创建多类型组合 Token
		token = globalBalancer.AddNewTokenWithMultipleTypes(req.Limit, req.Types)
	} else {
		// 创建单类型 Token
		token = globalBalancer.AddNewToken(req.Limit, req.OriginalType)
	}

	_, description := globalBalancer.GetCapabilityInfo(token.OriginalType)
	if token.OriginalType == -1 {
		_, description = globalBalancer.GetCombinedCapabilityInfo(req.Types)
	}

	c.JSON(http.StatusCreated, APIResponse{
		Code:    201,
		Message: "Token created successfully",
		Data: gin.H{
			"id":              token.ID,
			"limit":           token.Limit,
			"used":            token.Used,
			"conn_num":        token.ConnNum,
			"original_type":   token.OriginalType,
			"capability_bits": token.CapabilityBits,
			"capabilities":    description,
		},
	})
}

// 获取所有 Token
func listTokens(c *gin.Context) {
	var tokens []gin.H
	for _, token := range globalBalancer.Tokens {
		_, description := globalBalancer.GetCapabilityInfo(token.OriginalType)
		if token.OriginalType == -1 {
			description = getCapabilityDescription(token.CapabilityBits)
		}

		tokens = append(tokens, gin.H{
			"id":              token.ID,
			"limit":           token.Limit,
			"used":            token.Used,
			"conn_num":        token.ConnNum,
			"original_type":   token.OriginalType,
			"capability_bits": token.CapabilityBits,
			"capabilities":    description,
		})
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Tokens retrieved successfully",
		Data:    tokens,
	})
}

// 获取单个 Token
func getToken(c *gin.Context) {
	tokenID := c.Param("id")
	token, exists := globalBalancer.Tokens[tokenID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Token not found",
		})
		return
	}

	_, description := globalBalancer.GetCapabilityInfo(token.OriginalType)
	if token.OriginalType == -1 {
		description = getCapabilityDescription(token.CapabilityBits)
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Token retrieved successfully",
		Data: gin.H{
			"id":              token.ID,
			"limit":           token.Limit,
			"used":            token.Used,
			"conn_num":        token.ConnNum,
			"original_type":   token.OriginalType,
			"capability_bits": token.CapabilityBits,
			"capabilities":    description,
		},
	})
}

// 更新 Token
func updateToken(c *gin.Context) {
	tokenID := c.Param("id")
	token, exists := globalBalancer.Tokens[tokenID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Token not found",
		})
		return
	}

	var req struct {
		Limit *int `json:"limit,omitempty"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	if req.Limit != nil && *req.Limit > 0 {
		token.Limit = *req.Limit
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Token updated successfully",
		Data: gin.H{
			"id":    token.ID,
			"limit": token.Limit,
			"used":  token.Used,
		},
	})
}

// 删除 Token
func deleteToken(c *gin.Context) {
	tokenID := c.Param("id")
	_, exists := globalBalancer.Tokens[tokenID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Token not found",
		})
		return
	}

	delete(globalBalancer.Tokens, tokenID)
	delete(globalBalancer.UnbindedTokens, tokenID)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Token deleted successfully",
	})
}

// Receiver CRUD 操作

// 创建 Receiver
func createReceiver(c *gin.Context) {
	var req ReceiverRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	var receiver *Receiver
	if req.OriginalType != nil {
		receiver = globalBalancer.AddNewReceiver(*req.OriginalType)
	} else {
		receiver = globalBalancer.AddNewReceiver() // 默认 stock_a
	}

	_, description := globalBalancer.GetCapabilityInfo(receiver.OriginalType)

	c.JSON(http.StatusCreated, APIResponse{
		Code:    201,
		Message: "Receiver created successfully",
		Data: gin.H{
			"id":              receiver.ID,
			"token_id":        receiver.TokenID,
			"used":            receiver.Used,
			"original_type":   receiver.OriginalType,
			"capability_bits": receiver.CapabilityBits,
			"capabilities":    description,
		},
	})
}

// 获取所有 Receiver
func listReceivers(c *gin.Context) {
	var receivers []gin.H
	for _, receiver := range globalBalancer.Receivers {
		_, description := globalBalancer.GetCapabilityInfo(receiver.OriginalType)

		receivers = append(receivers, gin.H{
			"id":              receiver.ID,
			"token_id":        receiver.TokenID,
			"used":            receiver.Used,
			"original_type":   receiver.OriginalType,
			"capability_bits": receiver.CapabilityBits,
			"capabilities":    description,
		})
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receivers retrieved successfully",
		Data:    receivers,
	})
}

// 获取单个 Receiver
func getReceiver(c *gin.Context) {
	receiverID := c.Param("id")
	receiver, exists := globalBalancer.Receivers[receiverID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Receiver not found",
		})
		return
	}

	_, description := globalBalancer.GetCapabilityInfo(receiver.OriginalType)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receiver retrieved successfully",
		Data: gin.H{
			"id":              receiver.ID,
			"token_id":        receiver.TokenID,
			"used":            receiver.Used,
			"original_type":   receiver.OriginalType,
			"capability_bits": receiver.CapabilityBits,
			"capabilities":    description,
		},
	})
}

// 更新 Receiver
func updateReceiver(c *gin.Context) {
	receiverID := c.Param("id")
	receiver, exists := globalBalancer.Receivers[receiverID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Receiver not found",
		})
		return
	}

	var req struct {
		Used *int `json:"used,omitempty"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	if req.Used != nil && *req.Used >= 0 {
		oldUsed := receiver.Used
		receiver.Used = *req.Used

		// 如果使用量变化，需要更新可用切片
		if oldUsed < 500 && receiver.Used >= 500 {
			// 从可用切片中移除
			globalBalancer.removeFromAvailableReceivers(receiver)
		} else if oldUsed >= 500 && receiver.Used < 500 {
			// 添加到可用切片
			globalBalancer.addToAvailableReceivers(receiver)
		}
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receiver updated successfully",
		Data: gin.H{
			"id":   receiver.ID,
			"used": receiver.Used,
		},
	})
}

// 删除 Receiver
func deleteReceiver(c *gin.Context) {
	receiverID := c.Param("id")
	receiver, exists := globalBalancer.Receivers[receiverID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Receiver not found",
		})
		return
	}

	// 从可用切片中移除
	globalBalancer.removeFromAvailableReceivers(receiver)

	// 如果绑定了token，减少连接数
	if receiver.TokenID != "" {
		if token, exists := globalBalancer.Tokens[receiver.TokenID]; exists {
			token.ConnNum--
		}
	}

	delete(globalBalancer.Receivers, receiverID)
	delete(globalBalancer.UnbindedReceivers, receiverID)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receiver deleted successfully",
	})
}

// Product 相关操作

// 分配产品
func allocateProduct(c *gin.Context) {
	var req ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	receiverID, tokenID := globalBalancer.AllocateProduct(req.ProductType)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Product allocated successfully",
		Data: gin.H{
			"receiver_id":  receiverID,
			"token_id":     tokenID,
			"product_type": req.ProductType,
		},
	})
}

// 获取产品统计信息
func getProductStats(c *gin.Context) {
	stats := gin.H{
		"total_tokens":                len(globalBalancer.Tokens),
		"total_receivers":             len(globalBalancer.Receivers),
		"unbinded_tokens":             len(globalBalancer.UnbindedTokens),
		"unbinded_receivers":          len(globalBalancer.UnbindedReceivers),
		"available_receivers_by_type": make(map[string]int),
		"token_usage":                 make([]gin.H, 0),
		"receiver_usage":              make([]gin.H, 0),
	}

	// 统计按类型分组的可用receiver数量
	availableByType := stats["available_receivers_by_type"].(map[string]int)
	for typeNum, receivers := range globalBalancer.AvailableReceiversByType {
		availableByType[strconv.Itoa(typeNum)] = len(receivers)
	}

	// Token使用情况统计
	tokenUsage := stats["token_usage"].([]gin.H)
	for _, token := range globalBalancer.Tokens {
		_, description := globalBalancer.GetCapabilityInfo(token.OriginalType)
		if token.OriginalType == -1 {
			description = getCapabilityDescription(token.CapabilityBits)
		}

		tokenUsage = append(tokenUsage, gin.H{
			"id":           token.ID,
			"used":         token.Used,
			"limit":        token.Limit,
			"usage_rate":   float64(token.Used) / float64(token.Limit),
			"conn_num":     token.ConnNum,
			"capabilities": description,
		})
	}
	stats["token_usage"] = tokenUsage

	// Receiver使用情况统计
	receiverUsage := stats["receiver_usage"].([]gin.H)
	for _, receiver := range globalBalancer.Receivers {
		receiverUsage = append(receiverUsage, gin.H{
			"id":         receiver.ID,
			"used":       receiver.Used,
			"usage_rate": float64(receiver.Used) / 500.0,
			"token_id":   receiver.TokenID,
		})
	}
	stats["receiver_usage"] = receiverUsage

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Product stats retrieved successfully",
		Data:    stats,
	})
}

// 系统配置相关操作

// 获取系统状态
func getSystemStatus(c *gin.Context) {
	status := gin.H{
		"mixed_mode":         globalBalancer.EnableMixedMode,
		"total_tokens":       len(globalBalancer.Tokens),
		"total_receivers":    len(globalBalancer.Receivers),
		"unbinded_tokens":    len(globalBalancer.UnbindedTokens),
		"unbinded_receivers": len(globalBalancer.UnbindedReceivers),
		"system_health":      "healthy",
	}

	// 计算系统负载
	totalUsed := 0
	totalLimit := 0
	for _, token := range globalBalancer.Tokens {
		totalUsed += token.Used
		totalLimit += token.Limit
	}

	if totalLimit > 0 {
		status["overall_usage_rate"] = float64(totalUsed) / float64(totalLimit)
	} else {
		status["overall_usage_rate"] = 0.0
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "System status retrieved successfully",
		Data:    status,
	})
}

// 设置混用模式
func setMixedMode(c *gin.Context) {
	var req struct {
		EnableMixedMode bool `json:"enable_mixed_mode"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	globalBalancer.SetMixedMode(req.EnableMixedMode)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Mixed mode updated successfully",
		Data: gin.H{
			"enable_mixed_mode": globalBalancer.EnableMixedMode,
		},
	})
}

// 获取指定类型的能力信息
func getCapabilities(c *gin.Context) {
	typeParam := c.Param("type")
	originalType, err := strconv.Atoi(typeParam)
	if err != nil || originalType < 1 || originalType > 6 {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid type parameter, must be between 1 and 6",
		})
		return
	}

	bits, description := globalBalancer.GetCapabilityInfo(originalType)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Capabilities retrieved successfully",
		Data: gin.H{
			"original_type":   originalType,
			"capability_bits": bits,
			"capabilities":    description,
		},
	})
}

// 启动 API 服务器
func StartAPIServer(balancer *Balancer, port string) {
	globalBalancer = balancer

	r := setupRoutes()

	// 启动服务器
	r.Run(":" + port)
}
