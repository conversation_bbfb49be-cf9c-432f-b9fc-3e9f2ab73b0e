package main

import (
	"strconv"
)

var (
	receiverCounter int
	tokenCounter    int
)

// 能力类型常量
const (
	CapabilityForex   = 1 << 0 // 1: forex
	CapabilityEnergy  = 1 << 1 // 2: energy
	CapabilityStockUS = 1 << 2 // 4: stock_us
	CapabilityStockHK = 1 << 3 // 8: stock_hk
	CapabilityStockA  = 1 << 4 // 16: stock_a
	CapabilityCrypto  = 1 << 5 // 32: crypto
)

// 能力映射：原始类型 -> 位标志
var capabilityMap = map[int]int{
	1: CapabilityForex,   // forex
	2: CapabilityEnergy,  // energy
	3: CapabilityStockUS, // stock_us
	4: CapabilityStockHK, // stock_hk
	5: CapabilityStockA,  // stock_a
	6: CapabilityCrypto,  // crypto
}

type Token struct {
	ID             string
	Limit          int
	Used           int
	ConnNum        int
	CapabilityBits int // 使用位运算表示支持的能力
	OriginalType   int // 原始类型，用于显示
}

type Receiver struct {
	ID             string
	TokenID        string
	Used           int
	CapabilityBits int // 使用位运算表示支持的能力
	OriginalType   int // 原始类型，用于显示
}

type Balancer struct {
	Receivers                map[string]*Receiver
	Tokens                   map[string]*Token
	UnbindedTokens           map[string]*Token    // 未绑定的 token
	UnbindedReceivers        map[string]*Receiver // 未绑定的 receiver
	AvailableReceiversByType map[int][]*Receiver  // 按原始类型分组的可用 receiver 切片
	EnableMixedMode          bool                 // 是否启用混用模式
}

func NewBalancer() *Balancer {
	return &Balancer{
		Receivers:                make(map[string]*Receiver),
		Tokens:                   make(map[string]*Token),
		UnbindedTokens:           make(map[string]*Token),
		UnbindedReceivers:        make(map[string]*Receiver),
		AvailableReceiversByType: make(map[int][]*Receiver),
		EnableMixedMode:          false, // 默认不启用混用模式
	}
}

// 计算能力位标志
func calculateCapabilityBits(originalType int) int {
	bits := capabilityMap[originalType]

	// 特殊规则：
	// stock_us(3) 支持 stock_us 和 stock_a
	// stock_hk(4) 支持 stock_hk 和 stock_a
	// stock_a(5) 只支持 stock_a
	if originalType == 3 { // stock_us
		bits = CapabilityStockUS | CapabilityStockA
	} else if originalType == 4 { // stock_hk
		bits = CapabilityStockHK | CapabilityStockA
	}

	return bits
}

// 计算多类型组合的能力位标志
func calculateCombinedCapabilityBits(types []int) int {
	var combinedBits int
	for _, t := range types {
		combinedBits |= calculateCapabilityBits(t)
	}
	return combinedBits
}

// 检查是否支持指定能力
func hasCapability(capabilityBits int, targetType int) bool {
	targetBits := capabilityMap[targetType]
	return (capabilityBits & targetBits) != 0
}

// 检查两个能力是否兼容（完全匹配或混用模式下的兼容）
func isCapabilityCompatible(tokenBits, receiverBits int, enableMixed bool) bool {
	if enableMixed {
		// 混用模式：只要有任何重叠的能力就兼容
		return (tokenBits & receiverBits) != 0
	} else {
		// 严格模式：必须完全匹配
		return tokenBits == receiverBits
	}
}

func (b *Balancer) AddNewToken(limit, originalType int) *Token {
	tokenID := generateTokenID()
	token := &Token{
		ID:             tokenID,
		Limit:          limit,
		Used:           0,
		CapabilityBits: calculateCapabilityBits(originalType),
		OriginalType:   originalType,
	}
	b.Tokens[tokenID] = token
	b.tryBindToken(tokenID)
	return token
}

// 创建支持多种类型的 Token (例如: stock_us+stock_hk)
func (b *Balancer) AddNewTokenWithMultipleTypes(limit int, types []int) *Token {
	tokenID := generateTokenID()
	token := &Token{
		ID:             tokenID,
		Limit:          limit,
		Used:           0,
		CapabilityBits: calculateCombinedCapabilityBits(types),
		OriginalType:   -1, // 使用 -1 表示多类型组合
	}
	b.Tokens[tokenID] = token
	b.tryBindToken(tokenID)
	return token
}

/*
增加Token逻辑：
1. 增加Token时，先判断是否有待绑定的receiver,如果没有，放入未绑定的map中，返回
2. 如果有并且能力兼容，直接绑定；将receiver从待绑定map中删除，然后将receiver加入到对应类型的可用切片中
3. 如果能力不兼容，放入未绑定的map中
*/
func (b *Balancer) tryBindToken(tokenID string) {
	curToken := b.Tokens[tokenID]
	if len(b.UnbindedReceivers) == 0 {
		b.UnbindedTokens[tokenID] = curToken
		return
	}

	// 查找匹配的 receiver 并绑定
	for receiverID, receiver := range b.UnbindedReceivers {
		if isCapabilityCompatible(curToken.CapabilityBits, receiver.CapabilityBits, b.EnableMixedMode) {
			// 绑定 receiver 到 token
			receiver.TokenID = tokenID
			curToken.ConnNum++

			// 将 receiver 加入到对应类型的可用切片中（如果未满）
			if receiver.Used < 500 {
				b.addToAvailableReceivers(receiver)
			}

			// 从未绑定列表中移除
			delete(b.UnbindedReceivers, receiverID)
			return // 只绑定一个 receiver
		}
	}

	// 没有找到匹配的 receiver，将 token 放入未绑定列表
	b.UnbindedTokens[tokenID] = curToken
}

// 将 receiver 添加到对应类型的可用切片中
func (b *Balancer) addToAvailableReceivers(receiver *Receiver) {
	capType := receiver.OriginalType
	b.AvailableReceiversByType[capType] = append(b.AvailableReceiversByType[capType], receiver)
}

// 从对应类型的可用切片中移除 receiver
func (b *Balancer) removeFromAvailableReceivers(receiver *Receiver) {
	capType := receiver.OriginalType
	receivers := b.AvailableReceiversByType[capType]
	for i, r := range receivers {
		if r.ID == receiver.ID {
			// 移除该元素
			b.AvailableReceiversByType[capType] = append(receivers[:i], receivers[i+1:]...)
			break
		}
	}
}

/*
增加Receiver逻辑：
1. 增加Receiver时，先判断是否有未使用并且能力兼容的token
2. 如果可以绑定，直接绑定，并将receiver加入到对应类型的可用切片中
3. 如果没有匹配的token，将receiver放入未绑定的map中
*/
func (b *Balancer) tryBindReceiver(receiverID string) {
	curReceiver := b.Receivers[receiverID]

	// 查找匹配的未绑定 token
	for tokenID, token := range b.UnbindedTokens {
		if isCapabilityCompatible(token.CapabilityBits, curReceiver.CapabilityBits, b.EnableMixedMode) && token.ConnNum < 10 {
			// 绑定 receiver 到 token
			curReceiver.TokenID = tokenID
			token.ConnNum++

			// 将 receiver 加入到对应类型的可用切片中（如果未满）
			if curReceiver.Used < 500 {
				b.addToAvailableReceivers(curReceiver)
			}

			// 从未绑定列表中移除 token
			delete(b.UnbindedTokens, tokenID)
			return // 只绑定一个 token
		}
	}

	// 没有找到匹配的 token，将 receiver 放入未绑定列表
	b.UnbindedReceivers[receiverID] = curReceiver
}

func (b *Balancer) AddNewReceiver(originalType ...int) *Receiver {
	// 如果不传参数，默认为 stock_a (类型5)
	var receiverType int
	if len(originalType) == 0 {
		receiverType = 5 // stock_a
	} else {
		receiverType = originalType[0]
	}

	receiverID := generateReceiverID()
	receiver := &Receiver{
		ID:             receiverID,
		TokenID:        "", // 初始时未绑定
		Used:           0,
		CapabilityBits: calculateCapabilityBits(receiverType),
		OriginalType:   receiverType,
	}
	b.Receivers[receiverID] = receiver
	b.tryBindReceiver(receiverID)
	return receiver
}

/*
增加产品分配逻辑：
1. 优先从对应类型的可用Receiver切片中取出第一个接收器进行绑定
2. 如果启用混用模式且没有完全匹配的receiver，尝试兼容类型的receiver
3. 绑定Receiver后，更新Receiver和Token已分配产品的数量
4. 如果receiver满了（Used >= 500），从可用切片中移除
5. 如果找不到可用的receiver，创建新的receiver和token
*/
func (b *Balancer) AllocateProduct(productType int) (receiverID, tokenID string) {
	// 阶段1：尝试完全匹配的receiver（优先级最高）
	availableReceivers := b.AvailableReceiversByType[productType]
	if len(availableReceivers) > 0 {
		receiver := availableReceivers[0]
		token := b.Tokens[receiver.TokenID]

		// 检查receiver和token是否还有容量，并且token支持该产品类型
		if receiver.Used < 500 && token.Used < token.Limit && hasCapability(token.CapabilityBits, productType) {
			// 分配产品
			receiver.Used++
			token.Used++

			// 如果receiver满了，从可用切片中移除
			if receiver.Used >= 500 {
				b.removeFromAvailableReceivers(receiver)
			}

			return receiver.ID, receiver.TokenID
		} else {
			// receiver或token已满或不支持该产品类型，从可用切片中移除
			b.removeFromAvailableReceivers(receiver)
			// 递归调用，尝试下一个receiver
			return b.AllocateProduct(productType)
		}
	}

	// 阶段2：如果启用混用模式，尝试兼容类型的receiver
	if b.EnableMixedMode {
		for _, receivers := range b.AvailableReceiversByType {
			if len(receivers) > 0 {
				receiver := receivers[0]
				token := b.Tokens[receiver.TokenID]

				// 检查token是否支持该产品类型
				if receiver.Used < 500 && token.Used < token.Limit && hasCapability(token.CapabilityBits, productType) {
					// 分配产品
					receiver.Used++
					token.Used++

					// 如果receiver满了，从可用切片中移除
					if receiver.Used >= 500 {
						b.removeFromAvailableReceivers(receiver)
					}

					return receiver.ID, receiver.TokenID
				}
			}
		}
	}

	// 阶段3：没有可用的receiver，需要创建新的
	// 首先尝试找到有容量且支持该产品类型的token
	var availableToken *Token
	for _, token := range b.Tokens {
		if hasCapability(token.CapabilityBits, productType) && token.Used < token.Limit && token.ConnNum < 10 {
			availableToken = token
			break
		}
	}

	// 如果没有合适的token，创建新token
	if availableToken == nil {
		availableToken = b.AddNewToken(500, productType)
	}

	// 创建新receiver
	newReceiver := b.AddNewReceiver(productType)
	newReceiver.TokenID = availableToken.ID
	availableToken.ConnNum++

	// 分配产品
	newReceiver.Used = 1
	availableToken.Used++

	// 如果receiver还有容量，加入可用切片
	if newReceiver.Used < 500 {
		b.addToAvailableReceivers(newReceiver)
	}

	return newReceiver.ID, availableToken.ID
}

// 设置混用模式
func (b *Balancer) SetMixedMode(enabled bool) {
	b.EnableMixedMode = enabled
}

// 获取能力信息的辅助函数
func (b *Balancer) GetCapabilityInfo(originalType int) (bits int, description string) {
	bits = calculateCapabilityBits(originalType)
	description = getCapabilityDescription(bits)
	return bits, description
}

// 获取多类型组合的能力信息
func (b *Balancer) GetCombinedCapabilityInfo(types []int) (bits int, description string) {
	bits = calculateCombinedCapabilityBits(types)
	description = getCapabilityDescription(bits)
	return bits, description
}

// 根据能力位标志生成描述
func getCapabilityDescription(bits int) string {
	var capabilities []string
	if bits&CapabilityForex != 0 {
		capabilities = append(capabilities, "forex")
	}
	if bits&CapabilityEnergy != 0 {
		capabilities = append(capabilities, "energy")
	}
	if bits&CapabilityStockUS != 0 {
		capabilities = append(capabilities, "stock_us")
	}
	if bits&CapabilityStockHK != 0 {
		capabilities = append(capabilities, "stock_hk")
	}
	if bits&CapabilityStockA != 0 {
		capabilities = append(capabilities, "stock_a")
	}
	if bits&CapabilityCrypto != 0 {
		capabilities = append(capabilities, "crypto")
	}

	description := ""
	for i, cap := range capabilities {
		if i > 0 {
			description += ", "
		}
		description += cap
	}

	return description
}

func generateReceiverID() string {
	receiverCounter++
	return "receiver-" + strconv.Itoa(receiverCounter)
}

func generateTokenID() string {
	tokenCounter++
	return "token-" + strconv.Itoa(tokenCounter)
}
