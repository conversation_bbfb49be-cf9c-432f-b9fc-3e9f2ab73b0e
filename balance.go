package main

import (
	"math"
	"strconv"
)

var (
	receiverCounter int
	tokenCounter    int
)

type Token struct {
	ID                 string
	Limit              int
	Used               int
	ConnNum            int
	Capbility          int //forex: 1, energy: 2, stock_us: 3, stock_hk: 4, stock_a: 5, crypto: 6
	AssociatedReceiver []*Receiver
}

type Receiver struct {
	ID          string
	TokenID     string
	Used        int
	Capbility   int //forex: 1, energy: 2, stock_us: 3, stock_hk: 4, stock_a: 5, crypto: 6
	BindTokenID string
}

type Balancer struct {
	Receivers           map[string]*Receiver
	Tokens              map[string]*Token
	ReceiverBindedMap   map[string]*Token
	ReceiverUnbindedMap map[string]*Token
	TokenBindedMap      map[string]*Receiver
	TokenUnbindedMap    map[string]*Receiver
	NotFullTokenMap     map[string]*Token
}

func NewBalancer() *Balancer {
	return &Balancer{
		Receivers:           make(map[string]*Receiver),
		Tokens:              make(map[string]*Token),
		ReceiverBindedMap:   make(map[string]*Token),
		ReceiverUnbindedMap: make(map[string]*Token),
		TokenBindedMap:      make(map[string]*Receiver),
		TokenUnbindedMap:    make(map[string]*Receiver),
		NotFullTokenMap:     make(map[string]*Token),
	}
}

func (b *Balancer) AddNewToken(limit, capbility int) *Token {
	tokenID := generateTokenID()
	token := &Token{
		ID:        tokenID,
		Limit:     limit,
		Used:      0,
		Capbility: capbility,
	}
	b.Tokens[tokenID] = token
	b.tryBindToken(tokenID)
	return token
}

/*
增加Token逻辑：
1. 增加Token时，先判断是否有待绑定的receiver,如果没有，放入未绑定的map中，返回
2. 如果有并且capbility相同，直接绑定；将receiver从待绑定map中删除，然后将自己放到未满map中
3. 如果capbility不同，放入未绑定的map中
*/
func (b *Balancer) tryBindToken(tokenID string) {
	curToken := b.Tokens[tokenID]
	if len(b.ReceiverUnbindedMap) == 0 {
		b.TokenUnbindedMap[tokenID] = &Receiver{}
		return
	}
	for receiverID := range b.ReceiverUnbindedMap {
		if b.Receivers[receiverID].Capbility == curToken.Capbility {
			b.ReceiverBindedMap[receiverID] = curToken
			curToken.ConnNum++
			if curToken.ConnNum < 10 {
				b.NotFullTokenMap[tokenID] = curToken
			}
			delete(b.ReceiverUnbindedMap, receiverID)
			b.TokenBindedMap[tokenID] = b.Receivers[receiverID]
		}
	}
}

/*
增加Receiver逻辑：
1. 增加Receiver时，先判断是否有未使用并且capbility相同的token
2. 如果可以绑定，直接绑定
3. 如果不可以直接绑定，查找未满的token
4. 如果有未满的token，绑定到未满的token
5. 如果没有未满的token，放入未绑定的map中
6. 在增加token时，尝试绑定未绑定的receiver
*/
func (b *Balancer) tryBindReceiver(receiverID string) {
	curReceiver := b.Receivers[receiverID]
	for tokenID := range b.TokenUnbindedMap {
		if b.Tokens[tokenID].Capbility == curReceiver.Capbility {
			b.TokenBindedMap[tokenID] = curReceiver
			b.Tokens[tokenID].ConnNum++
			if b.Tokens[tokenID].ConnNum < 10 {
				b.NotFullTokenMap[tokenID] = b.Tokens[tokenID]
			}
			delete(b.TokenUnbindedMap, tokenID)
			b.ReceiverBindedMap[receiverID] = b.Tokens[tokenID]
		}
	}
}

func (b *Balancer) AddNewReceiver(tokenID string) *Receiver {
	receiverID := generateReceiverID()
	receiver := &Receiver{
		ID:      receiverID,
		TokenID: tokenID,
		Used:    0,
	}
	b.Receivers[receiverID] = receiver
	b.tryBindReceiver(receiverID)
	return receiver
}


/*  */
func (b *Balancer) AllocateProduct() (receiverID, tokenID string) {
	// 尝试使用现有接收器（优先选择负载最轻的）
	bestScore := math.MaxFloat64
	var bestReceiver *Receiver
	for _, r := range b.ReceiverBindedMap {
		token, exists := b.Tokens[r.TokenID]
		if !exists {
			continue
		}
		if r.Used+1 <= 500 && token.Used+1 <= token.Limit {
			// 评估分数：选择（接收器使用率 + token使用率）最低的组合
			receiverUtil := float64(r.Used) / 500
			tokenUtil := float64(token.Used) / float64(token.Limit)
			score := receiverUtil + tokenUtil
			if score < bestScore {
				bestScore = score
				bestReceiver = r
			}
		}
	}
	if bestReceiver != nil {
		token := b.Tokens[bestReceiver.TokenID]
		bestReceiver.Used++
		token.Used++
		return bestReceiver.ID, bestReceiver.TokenID
	}

	// 找不到合适的现有接收器，创建新接收器并分配令牌
	// 优先选择有容量的现有令牌
	bestTokenScore := math.MaxFloat64
	var bestToken *Token
	for _, t := range b.NotFullTokenMap {
		if t.Used+1 <= t.Limit && t.ConnNum < 10 {
			util := float64(t.Used) / float64(t.Limit)
			if util < bestTokenScore {
				bestTokenScore = util
				bestToken = t
			}
		}
	}
	if bestToken == nil {
		// 没有合适的令牌，创建新令牌（默认限额500）
		bestToken = b.AddNewToken(500, 1)
	}

	// 创建新接收器并关联令牌
	newReceiver := b.AddNewReceiver(bestToken.ID)
	newReceiver.Used = 1
	bestToken.Used++
	bestToken.ConnNum++

	return newReceiver.ID, bestToken.ID
}

func generateReceiverID() string {
	receiverCounter++
	return "receiver-" + strconv.Itoa(receiverCounter)
}

func generateTokenID() string {
	tokenCounter++
	return "token-" + strconv.Itoa(tokenCounter)
}
