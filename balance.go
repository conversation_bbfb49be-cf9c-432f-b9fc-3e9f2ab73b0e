package main

import (
	"strconv"
)

var (
	receiverCounter int
	tokenCounter    int
)

type Token struct {
	ID        string
	Limit     int
	Used      int
	ConnNum   int
	Capbility int //forex: 1, energy: 2, stock_us: 3, stock_hk: 4, stock_a: 5, crypto: 6
}

type Receiver struct {
	ID        string
	TokenID   string
	Used      int
	Capbility int //forex: 1, energy: 2, stock_us: 3, stock_hk: 4, stock_a: 5, crypto: 6
}

type Balancer struct {
	Receivers                map[string]*Receiver
	Tokens                   map[string]*Token
	UnbindedTokens           map[string]*Token    // 未绑定的 token
	UnbindedReceivers        map[string]*Receiver // 未绑定的 receiver
	AvailableReceiversByType map[int][]*Receiver  // 按类型分组的可用 receiver 切片
}

func NewBalancer() *Balancer {
	return &Balancer{
		Receivers:                make(map[string]*Receiver),
		Tokens:                   make(map[string]*Token),
		UnbindedTokens:           make(map[string]*Token),
		UnbindedReceivers:        make(map[string]*Receiver),
		AvailableReceiversByType: make(map[int][]*Receiver),
	}
}

func (b *Balancer) AddNewToken(limit, capbility int) *Token {
	tokenID := generateTokenID()
	token := &Token{
		ID:        tokenID,
		Limit:     limit,
		Used:      0,
		Capbility: capbility,
	}
	b.Tokens[tokenID] = token
	b.tryBindToken(tokenID)
	return token
}

/*
增加Token逻辑：
1. 增加Token时，先判断是否有待绑定的receiver,如果没有，放入未绑定的map中，返回
2. 如果有并且capbility相同，直接绑定；将receiver从待绑定map中删除，然后将receiver加入到对应类型的可用切片中
3. 如果capbility不同，放入未绑定的map中
*/
func (b *Balancer) tryBindToken(tokenID string) {
	curToken := b.Tokens[tokenID]
	if len(b.UnbindedReceivers) == 0 {
		b.UnbindedTokens[tokenID] = curToken
		return
	}

	// 查找匹配的 receiver 并绑定
	for receiverID, receiver := range b.UnbindedReceivers {
		if receiver.Capbility == curToken.Capbility {
			// 绑定 receiver 到 token
			receiver.TokenID = tokenID
			curToken.ConnNum++

			// 将 receiver 加入到对应类型的可用切片中（如果未满）
			if receiver.Used < 500 {
				b.addToAvailableReceivers(receiver)
			}

			// 从未绑定列表中移除
			delete(b.UnbindedReceivers, receiverID)
			return // 只绑定一个 receiver
		}
	}

	// 没有找到匹配的 receiver，将 token 放入未绑定列表
	b.UnbindedTokens[tokenID] = curToken
}

// 将 receiver 添加到对应类型的可用切片中
func (b *Balancer) addToAvailableReceivers(receiver *Receiver) {
	capType := receiver.Capbility
	b.AvailableReceiversByType[capType] = append(b.AvailableReceiversByType[capType], receiver)
}

// 从对应类型的可用切片中移除 receiver
func (b *Balancer) removeFromAvailableReceivers(receiver *Receiver) {
	capType := receiver.Capbility
	receivers := b.AvailableReceiversByType[capType]
	for i, r := range receivers {
		if r.ID == receiver.ID {
			// 移除该元素
			b.AvailableReceiversByType[capType] = append(receivers[:i], receivers[i+1:]...)
			break
		}
	}
}

/*
增加Receiver逻辑：
1. 增加Receiver时，先判断是否有未使用并且capbility相同的token
2. 如果可以绑定，直接绑定，并将receiver加入到对应类型的可用切片中
3. 如果没有匹配的token，将receiver放入未绑定的map中
*/
func (b *Balancer) tryBindReceiver(receiverID string) {
	curReceiver := b.Receivers[receiverID]

	// 查找匹配的未绑定 token
	for tokenID, token := range b.UnbindedTokens {
		if token.Capbility == curReceiver.Capbility && token.ConnNum < 10 {
			// 绑定 receiver 到 token
			curReceiver.TokenID = tokenID
			token.ConnNum++

			// 将 receiver 加入到对应类型的可用切片中（如果未满）
			if curReceiver.Used < 500 {
				b.addToAvailableReceivers(curReceiver)
			}

			// 从未绑定列表中移除 token
			delete(b.UnbindedTokens, tokenID)
			return // 只绑定一个 token
		}
	}

	// 没有找到匹配的 token，将 receiver 放入未绑定列表
	b.UnbindedReceivers[receiverID] = curReceiver
}

func (b *Balancer) AddNewReceiver(capbility int) *Receiver {
	receiverID := generateReceiverID()
	receiver := &Receiver{
		ID:        receiverID,
		TokenID:   "", // 初始时未绑定
		Used:      0,
		Capbility: capbility,
	}
	b.Receivers[receiverID] = receiver
	b.tryBindReceiver(receiverID)
	return receiver
}

/*
增加产品分配逻辑：
1. 增加产品时，从对应类型的可用Receiver切片中取出第一个接收器进行绑定
2. 绑定Receiver后，更新Receiver和Token已分配产品的数量
3. 如果receiver满了（Used >= 500），从可用切片中移除
4. 如果找不到可用的receiver，创建新的receiver和token
*/
func (b *Balancer) AllocateProduct(capbility int) (receiverID, tokenID string) {
	// 从对应类型的可用切片中获取第一个receiver
	availableReceivers := b.AvailableReceiversByType[capbility]
	if len(availableReceivers) > 0 {
		receiver := availableReceivers[0]
		token := b.Tokens[receiver.TokenID]

		// 检查receiver和token是否还有容量
		if receiver.Used < 500 && token.Used < token.Limit {
			// 分配产品
			receiver.Used++
			token.Used++

			// 如果receiver满了，从可用切片中移除
			if receiver.Used >= 500 {
				b.removeFromAvailableReceivers(receiver)
			}

			return receiver.ID, receiver.TokenID
		} else {
			// receiver或token已满，从可用切片中移除
			b.removeFromAvailableReceivers(receiver)
			// 递归调用，尝试下一个receiver
			return b.AllocateProduct(capbility)
		}
	}

	// 没有可用的receiver，需要创建新的
	// 首先尝试找到有容量的token
	var availableToken *Token
	for _, token := range b.Tokens {
		if token.Capbility == capbility && token.Used < token.Limit && token.ConnNum < 10 {
			availableToken = token
			break
		}
	}

	// 如果没有合适的token，创建新token
	if availableToken == nil {
		availableToken = b.AddNewToken(500, capbility)
	}

	// 创建新receiver
	newReceiver := b.AddNewReceiver(capbility)
	newReceiver.TokenID = availableToken.ID
	availableToken.ConnNum++

	// 分配产品
	newReceiver.Used = 1
	availableToken.Used++

	// 如果receiver还有容量，加入可用切片
	if newReceiver.Used < 500 {
		b.addToAvailableReceivers(newReceiver)
	}

	return newReceiver.ID, availableToken.ID
}

func generateReceiverID() string {
	receiverCounter++
	return "receiver-" + strconv.Itoa(receiverCounter)
}

func generateTokenID() string {
	tokenCounter++
	return "token-" + strconv.Itoa(tokenCounter)
}
